<!-- 容器 -->
<div class="stake-menu-container">
  <!-- 搜索栏 -->
  <div class="search-bar-sticky" *ngIf="showSearch">
    <div class="search-bar-parent">
      <ion-icon class="login-form-input-icon" name="search-outline"></ion-icon>
      <ion-input
        [placeholder]="searchPlaceholder"
        [(ngModel)]="params"
      >
      </ion-input>
    </div>
    <ion-note slot="end" class="title-end-operate" style="padding-right: 60px;" (click)="onReset()">
      重置
    </ion-note>
    <ion-note slot="end" class="title-end-operate" style="padding-right:10px;" (click)="onSearch()">
      搜索
    </ion-note>
  </div>

  <!-- 内容区域 - 使用 ion-content 支持无限滚动 -->
  <ion-content class="content-scrollable" [style.height.px]="contentHeight" [scrollEvents]="true">
    <!-- 优化的加载状态 -->
    <div *ngIf="loading" class="loading-container">
      <div class="loading-content">
        <ion-spinner name="bubbles" color="primary"></ion-spinner>
        <span class="loading-text">{{ loadingText }}</span>
      </div>
    </div>

    <div *ngIf="items.length<=0 && !loading;" class="no-data">
      <img src="assets/menu/box2.png" style="padding-top: 50px;" />
      <!-- 暂无数据 -->
      <span class="no-data-span">暂无数据</span>
    </div>

    <ost-tree-list
      *ngIf="items.length > 0"
      #menu
      [items]="items"
      (toggleSubMenu)="onToggleSubMenu($event)"
      (itemClick)="onItemClick($event)">
    </ost-tree-list>

    <!-- 加载更多 -->
    <ion-infinite-scroll *ngIf="isPage" threshold="50px" (ionInfinite)="loadMoreData($event)">
      <ion-infinite-scroll-content
        [loadingSpinner]="spinner"
        [loadingText]="isShowNoMoreStr">
      </ion-infinite-scroll-content>
    </ion-infinite-scroll>
  </ion-content>
</div>