import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { Platform } from '@ionic/angular';
import { map, takeUntil } from 'rxjs/operators';
import { PageGridResult } from 'src/app/@core/base/request-result';
import { OstTreeListComponent } from '../../ost-tree-list/ost-tree-list.component';
import { OstTreeListItem } from '../../ost-tree-list/ost-tree-list.service';
import { ShareModuleService } from '../../share.service';
import { Subject } from 'rxjs';

@Component({
  selector: 'ost-stake-menu',
  templateUrl: './stake-menu.component.html',
  styleUrls: ['./stake-menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StakeMenuComponent implements OnInit, OnChanges, OnDestroy {
  @ViewChild('menu', { static: false }) menu: OstTreeListComponent;

  /** 管线ID，决定数据加载范围 */
  @Input() pipelineId: string;
  /** 数据接口地址 */
  @Input() interfaceUrl: string;
  /** 显示的标签名称 */
  @Input() labelName: string;
  /** 是否分页模式，true为分页，false为一次性加载全部 */
  @Input() isPage: boolean;
  /** 是否显示搜索框 */
  @Input() showSearch: boolean = true;
  /** 搜索框占位符文本 */
  @Input() searchPlaceholder: string = '请输入桩号名称';
  /** 搜索字段名称，用于后端搜索 */
  @Input() searchFieldName: string = 'keyword';
  /** 加载提示文本 */
  @Input() loadingText: string = '数据加载中...';

  /** 子菜单展开/收起事件 */
  @Output() toggleSubMenu = new EventEmitter<any>();
  /** 菜单项点击事件 */
  @Output() itemClick = new EventEmitter<any>();

  /** 加载状态 */
  loading: boolean;
  /** 菜单数据项 */
  items: OstTreeListItem[] = [];
  /** 原始桩数据，用于搜索 */
  stakeTree: OstTreeListItem[] = [];
  /** 查询参数对象，包含分页与管线ID */
  searchData: StakeParams;
  /** 加载更多提示文本 */
  isShowNoMoreStr = '数据加载中...';
  /** 加载动画类型 */
  spinner = 'bubbles';
  /** 内容区域高度 */
  contentHeight: number;
  /** 搜索参数 */
  params = '';
  /** 是否正在搜索状态 */
  isSearching = false;
  /** 搜索防抖定时器 */
  private searchTimer: any;
  /** 组件销毁时用于取消订阅 */
  private destroy$ = new Subject<void>();

  constructor(private cd: ChangeDetectorRef, private netSer: ShareModuleService, public platform: Platform) { }

  /**
   * 组件初始化，首次加载数据
   */
  ngOnInit(): void {
    this.calculateContentHeight();
    // 初始化时不立即加载，等待 ngOnChanges 触发
  }

  /**
   * 计算内容区域高度
   */
  private calculateContentHeight(): void {
    const modalHeaderHeight = 56; // 模态框头部高度
    const searchBarHeight = this.showSearch ? 50 : 0; // 搜索框高度
    this.contentHeight = this.platform.height() - modalHeaderHeight - searchBarHeight;
  }

  /**
   * 输入属性变更时，重置查询参数并重新加载数据
   */
  ngOnChanges(changes: SimpleChanges): void {
    // 只有当 pipelineId 发生变化时才重新加载数据
    if (changes.pipelineId && this.pipelineId) {
      console.log(`📋 [桩菜单组件] pipelineId变更，重新加载数据: ${this.pipelineId}`);
      this.searchData = new StakeParams();
      this.loadMenuTree(this.pipelineId);
    }
  }

  /**
   * 根据ID设置选中项
   * @param id 目标项ID
   */
  setSelectItemById(id: string): void {
    const selectItem = this.items.find(itme => (itme.data.id === id));
    if (selectItem) {
      selectItem.expanded = true;
      this.menu.setSelectItem(selectItem);
      this.cd.markForCheck();
    }
  }

  /**
   * 直接设置选中项
   */
  setSelectItem(item: OstTreeListItem): void {
    this.menu.setSelectItem(item);
  }

  /**
   * 子菜单展开/收起事件处理
   */
  onToggleSubMenu(item: OstTreeListItem): void {
    this.toggleSubMenu.emit(item);
  }

  /**
   * 菜单项点击事件处理
   */
  onItemClick(item: OstTreeListItem): void {
    this.itemClick.emit(item);
  }

  /**
   * 加载桩数据，支持分页与非分页两种模式
   * @param id 管线ID
   */
  loadMenuTree(id: string): void {
    if (this.loading) {
      console.log(`⏳ [桩菜单组件] 正在加载中，跳过重复请求: ${id}`);
      return;
    }

    console.log(`🔄 [桩菜单组件] 开始加载桩数据: ${id}, 接口: ${this.interfaceUrl}`);

    // 重置加载更多状态
    this.spinner = 'bubbles';
    this.isShowNoMoreStr = '数据加载中...';

    const params = this.buildRequestParams(id);
    this.executeRequest(params, (res) => {
      console.log(`✅ [桩菜单组件] 桩数据加载完成: ${id}, 数据量: ${res?.length || 0}`);
      this.items = res;
      this.stakeTree = res; // 保存原始数据用于搜索
      this.setSelectItemById(id);
    });
  }

  /**
   * 构建请求参数
   */
  private buildRequestParams(pipelineId: string): any {
    const params: any = { ...this.searchData, pipelineId };
    if (!this.isPage) {
      delete params.pageIndex;
      delete params.pageSize;
    }
    return params;
  }

  /**
   * 执行网络请求
   */
  private executeRequest(params: any, onSuccess: (res: OstTreeListItem[]) => void): void {
    this.loading = true;
    this.netSer.getRequest({ interfaceUrl: this.interfaceUrl }, params)
      .pipe(
        map(data => this.transformation(data)),
        takeUntil(this.destroy$)
      )
      .subscribe({
        next: onSuccess,
        error: err => console.error('请求失败', err),
        complete: () => {
          this.loading = false;
          this.cd.markForCheck();
        }
      });
  }

  /**
   * 数据转换，将接口返回数据转为菜单项数组
   * @param stakeData 接口返回数据
   */
  transformation(stakeData: any): OstTreeListItem[] {
    // 分页模式取records，非分页模式直接取data
    const dataList = this.isPage ? stakeData?.data?.records : stakeData?.data;
    if (!Array.isArray(dataList)) return [];
    // 兼容后端字段变动，title字段可配置
    return dataList.map(i => ({
      title: i[this.labelName], // 兼容不同字段
      data: i
    }));
  }

  /**
   * 分页加载更多数据，仅分页模式下有效
   */
  loadMoreData(event: any): void {
    if (!this.isPage || this.loading) {
      event.target.complete();
      return;
    }

    this.searchData.pageIndex++;
    this.loading = true;

    this.netSer.getRequest({ interfaceUrl: this.interfaceUrl }, this.searchData)
      .pipe(
        map((data: PageGridResult<any[]>) => this.transformation(data)),
        takeUntil(this.destroy$)
      )
      .subscribe({
        next: res => {
          if (res?.length > 0) {
            this.items = [...this.items, ...res];
          } else {
            this.spinner = null;
            this.isShowNoMoreStr = '到底了';
          }
        },
        error: err => console.error('加载更多数据失败', err),
        complete: () => {
          this.loading = false;
          event.target.complete();
          this.cd.detectChanges();
        }
      });
  }

  /**
   * 重置搜索
   */
  onReset(): void {
    this.params = '';
    this.isSearching = false;

    if (this.isPage) {
      this.searchData = new StakeParams();
      this.loadMenuTree(this.pipelineId);
    } else {
      this.items = this.stakeTree;
    }
  }

  /**
   * 搜索功能
   */
  onSearch(): void {
    if (!this.params.trim()) {
      this.onReset();
      return;
    }

    this.isPage ? this.performBackendSearch() : this.performFrontendSearch();
  }

  /**
   * 清除搜索定时器
   */
  private clearSearchTimer(): void {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }

  /**
   * 执行后端搜索（分页模式）
   */
  private performBackendSearch(): void {
    if (this.loading) return;

    this.isSearching = true;
    // 重置加载更多状态
    this.spinner = 'bubbles';
    this.isShowNoMoreStr = '数据加载中...';

    // 更新搜索参数，用于后续的加载更多
    this.searchData = new StakeParams();
    this.searchData.pipelineId = this.pipelineId;
    (this.searchData as any)[this.searchFieldName] = this.params.trim();

    this.executeRequest(this.searchData, (res) => {
      this.items = res;
    });
  }

  /**
   * 执行前端搜索（非分页模式）
   */
  private performFrontendSearch(): void {
    this.isSearching = true;
    this.items = this.filterItems(this.stakeTree, this.params);
  }

  /**
   * 过滤搜索数据
   */
  private filterItems(items: OstTreeListItem[], query: string): OstTreeListItem[] {
    if (!items?.length || !query) return items;

    const lowerQuery = query.toLowerCase();
    return items.filter(item =>
      item.title?.toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * 组件销毁时释放资源，防止内存泄漏
   */
  ngOnDestroy(): void {
    this.clearSearchTimer();
    this.destroy$.next();
    this.destroy$.complete();
  }
}

/**
 * 查询参数对象，支持分页与管线ID
 */
export class StakeParams extends PageGridResult<any> {
  pageIndex = 1;   // 当前页码
  pageSize = 20;   // 每页条数
  pipelineId: string; // 管线ID
}