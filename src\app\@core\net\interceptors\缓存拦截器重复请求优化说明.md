# 缓存拦截器重复请求优化说明

## 问题描述

在"第三方施工"事件详情页面中，同时使用了两个 `search-source-stake` 组件（起始桩和终止桩），它们使用相同的接口参数：
- `interfaceUrl="/work-basic/api/v2/basic/stake/msg/grid"`
- `[pipelineId]="pipelineId"`
- `[isPage]="true"`
- `searchFieldName="stakeName"`

当页面加载时，两个组件几乎同时发起相同的网络请求，导致出现重复请求的问题。

## 原因分析

1. **时序问题**：两个组件在组件初始化时几乎同时调用 `loadMenuTree()` 方法
2. **缓存键冲突**：相同的请求参数生成相同的缓存键 `req.urlWithParams`
3. **缓存逻辑顺序**：原有逻辑先检查防抖，后检查缓存，导致并发请求无法有效共享

## 优化方案

### 1. 调整缓存检查顺序

**优化前**：
```typescript
// 先检查防抖
if (lastTime && currentTime - lastTime < 1000) {
  // 防抖逻辑
}

// 后检查缓存
if (this.requestCache.has(cacheKey) && !this.failedRequests.has(cacheKey)) {
  return this.requestCache.get(cacheKey)!;
}
```

**优化后**：
```typescript
// 先检查缓存（优先级更高）
if (this.requestCache.has(cacheKey) && !this.failedRequests.has(cacheKey)) {
  return this.requestCache.get(cacheKey)!;
}

// 后检查防抖
if (lastTime && currentTime - lastTime < 1000) {
  // 防抖逻辑
}
```

### 2. 增强错误处理

- 请求失败时立即清除缓存，避免缓存失败的 Observable
- 改进失败请求的清理逻辑

### 3. 添加调试日志

增加详细的调试日志，便于跟踪请求的缓存状态：
- 🔄 返回已缓存的请求
- ⚡ 防抖返回缓存
- 🚀 发起新请求
- ✅ 请求成功，缓存响应
- ❌ 请求失败
- 💾 缓存响应数据
- 🗑️ 缓存过期清理

## 优化效果

1. **减少重复请求**：相同参数的并发请求会共享同一个 Observable
2. **提高响应速度**：第二个及后续相同请求直接返回缓存的 Observable
3. **降低服务器压力**：避免不必要的重复网络请求
4. **改善用户体验**：减少加载时间，提高页面响应速度

## 使用场景

此优化特别适用于以下场景：
- 同一页面多个组件使用相同接口
- 快速连续的相同请求
- 分页组件的重复加载
- 搜索组件的防抖处理

## 注意事项

1. **调试日志**：生产环境建议移除或条件化显示调试日志
2. **缓存时间**：当前设置为600毫秒，可根据实际需求调整
3. **内存管理**：组件销毁时会自动清理所有缓存
4. **禁用缓存**：某些特殊接口（如登录、文件上传）已配置为禁用缓存

## 组件级别优化

除了缓存拦截器优化外，还对 `StakeMenuComponent` 进行了以下改进：

### 1. 避免重复的生命周期调用

**优化前**：
```typescript
ngOnInit(): void {
  if (this.pipelineId) {
    this.loadMenuTree(this.pipelineId); // 可能重复调用
  }
}

ngOnChanges(_changes: SimpleChanges): void {
  if (this.pipelineId) {
    this.loadMenuTree(this.pipelineId); // 可能重复调用
  }
}
```

**优化后**：
```typescript
ngOnInit(): void {
  this.calculateContentHeight();
  // 初始化时不立即加载，等待 ngOnChanges 触发
}

ngOnChanges(changes: SimpleChanges): void {
  // 只有当 pipelineId 发生变化时才重新加载数据
  if (changes.pipelineId && this.pipelineId) {
    this.searchData = new StakeParams();
    this.loadMenuTree(this.pipelineId);
  }
}
```

### 2. 增强加载状态检查

在 `loadMenuTree` 方法中增加了更严格的加载状态检查，避免并发请求：

```typescript
loadMenuTree(id: string): void {
  if (this.loading) {
    console.log(`⏳ [桩菜单组件] 正在加载中，跳过重复请求: ${id}`);
    return;
  }
  // ... 其他逻辑
}
```

## 测试验证

可以通过以下方式验证优化效果：

### 1. 网络请求验证
1. 打开浏览器开发者工具的 Network 面板
2. 进入"第三方施工"事件详情页面
3. 观察是否只有一个 `/work-basic/api/v2/basic/stake/msg/grid` 请求

### 2. 控制台日志验证
查看控制台日志，确认缓存机制正常工作：
- `🔄 [缓存拦截器] 返回已缓存的请求` - 表示成功使用缓存
- `📋 [桩菜单组件] pipelineId变更，重新加载数据` - 组件级别的加载控制
- `⏳ [桩菜单组件] 正在加载中，跳过重复请求` - 避免并发请求

### 3. 性能验证
- 页面加载速度应该有所提升
- 服务器请求数量减少
- 用户体验更加流畅

## 清理调试日志

在确认优化效果后，建议移除调试日志以保持代码整洁：

```typescript
// 移除这些调试日志
console.log(`🔄 [缓存拦截器] 返回已缓存的请求: ${cacheKey}`);
console.log(`📋 [桩菜单组件] pipelineId变更，重新加载数据: ${this.pipelineId}`);
// ... 其他调试日志
```
