import { Injectable, OnDestroy } from '@angular/core';
import { HttpEvent, HttpHandler, HttpInterceptor, HttpRequest, HttpResponse } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { catchError, shareReplay, tap } from 'rxjs/operators';

@Injectable()
export class CachingInterceptor implements HttpInterceptor, OnDestroy {
  // 存储需要禁用缓存的 URL 列表
  private readonly disableCacheUrls: string[] = [
    '/file/fileDes',
    '/work-inspect/api/v2/inspect/app/user/login'
  ];
  
  // 请求缓存 <请求标识, 响应的Observable>
  private requestCache = new Map<string, Observable<HttpEvent<any>>>();
  // 记录每个请求的最后请求时间，用于实现1秒内的防抖
  private lastRequestTimes = new Map<string, number>();
  // 记录失败的请求，避免缓存失败的Observable
  private failedRequests = new Set<string>();

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // 1. 对于禁用缓存的URL，直接跳过所有缓存逻辑
    if (this.isCacheDisabled(req.url)) {
      return next.handle(req);
    }

    // 2. 生成缓存key（注意：必须在添加任何动态参数如时间戳之前生成）
    const cacheKey = req.urlWithParams;

    // 3. 检查1秒内是否重复请求（防抖）
    const lastTime = this.lastRequestTimes.get(cacheKey);
    const currentTime = Date.now();
    if (lastTime && currentTime - lastTime < 1000) {
      const cachedResponse = this.requestCache.get(cacheKey);
      if (cachedResponse) {
        // 如果在1秒内且有缓存，则直接返回缓存的响应
        return cachedResponse;
      }
      // 如果在1秒内但没有缓存（可能是之前请求失败被清除），
      // 清除防抖记录，允许重新发起请求
      this.lastRequestTimes.delete(cacheKey);
    }

    // 4. 更新当前请求的时间
    this.lastRequestTimes.set(cacheKey, currentTime);
    
    // 5. 如果缓存中已存在且不是失败的请求，直接返回
    if (this.requestCache.has(cacheKey) && !this.failedRequests.has(cacheKey)) {
        return this.requestCache.get(cacheKey)!;
    }

    // 6. 如果是失败的请求，清除相关记录
    if (this.failedRequests.has(cacheKey)) {
        this.failedRequests.delete(cacheKey);
        this.requestCache.delete(cacheKey);
        this.lastRequestTimes.delete(cacheKey);
    }

    // 7. 发送新请求，并处理后续的缓存逻辑
    const newRequestObservable = next.handle(req).pipe(
      tap(event => {
        // 只缓存成功的GET请求的HttpResponse
        if (event instanceof HttpResponse && event.status === 200 && req.method === 'GET') {
          this.cacheResponse(cacheKey, event);
        }
      }),
      catchError(error => {
        // 如果请求出错，标记为失败请求，清除防抖记录以允许立即重试
        this.failedRequests.add(cacheKey);
        this.lastRequestTimes.delete(cacheKey);
        return throwError(() => error);
      }),
      shareReplay(1) // 确保对同一请求的多个订阅者共享一个响应
    );

    // 将新的请求流存入缓存
    this.requestCache.set(cacheKey, newRequestObservable);

    return newRequestObservable;
  }
  
  private cacheResponse(key: string, event: HttpResponse<any>) {
    // 克隆响应对象以避免副作用
    this.requestCache.set(key, of(event.clone()));
    // 设置600毫秒的缓存有效期
    setTimeout(() => {
        this.requestCache.delete(key);
        // 防抖计时器也一并清除，或根据需要保留
        this.lastRequestTimes.delete(key);
    }, 600);
  }

  private isCacheDisabled(url: string): boolean {
    return this.disableCacheUrls.some(disableUrl => url.includes(disableUrl));
  }

  ngOnDestroy() {
    this.requestCache.clear();
    this.lastRequestTimes.clear();
    this.failedRequests.clear();
  }
}