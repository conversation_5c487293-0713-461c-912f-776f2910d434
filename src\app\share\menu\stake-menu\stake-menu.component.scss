// 容器样式
.stake-menu-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 粘性搜索栏样式
.search-bar-sticky {
  height: 50px;
  width: 100%;
  display: flex;
  align-items: center;
  padding-left: 16px;
  position: sticky;
  top: 0;
  background-color: #f6f6f6;
  z-index: 100;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0; // 防止被压缩

  p {
    height: 40px;
    line-height: 40px;
    position: absolute;
    font-size: 14px;
    right: 16px;
    color: #fff;
  }

  .search-bar-parent {
    color: black;
    width: 70%;
    height: 40px;
    padding: 0 15px;
    font-size: 14px;
    background-color: #fff;
    border-radius: 3px;
    display: flex;
    align-items: center;

    ::-webkit-input-placeholder {
      color: #666666;
    }

    .login-form-input-icon {
      padding-right: 8px;
      text-align: center;
      height: 50%;
    }

    ion-input {
      padding-left: 6px;
    }
  }

  .title-end-operate {
    color: black;
  }
}

// 可滚动内容区域样式
.content-scrollable {
  flex: 1;
  position: relative;

  // 重置 ion-content 的默认样式
  --padding-top: 0;
  --padding-bottom: 0;
  --padding-start: 0;
  --padding-end: 0;
}

// 加载状态样式
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  min-height: 120px;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    ion-spinner {
      width: 32px;
      height: 32px;
    }

    .loading-text {
      color: #666;
      font-size: 14px;
      font-weight: 400;
    }
  }
}

// 无数据状态样式
.no-data {
  text-align: center;
  padding: 20px;

  .no-data-span {
    display: block;
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}

.no-data {
  text-align: center;
  padding: 20px;

  .no-data-span {
    display: block;
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}